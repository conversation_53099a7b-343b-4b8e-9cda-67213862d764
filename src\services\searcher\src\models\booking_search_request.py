"""
Booking Search Request Model

This module defines the data model for a booking.com search request.
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class BookingSearchRequest(BaseModel):
    """
    Model representing a booking.com search request.
    """

    area: str = Field(..., description="The area to search for")
    guests: int = Field(2, description="Number of guests")
    check_in: str = Field(None, description="Check-in date in YYYY-MM-DD format")
    check_out: str = Field(None, description="Check-out date in YYYY-MM-DD format")

    def get_period_name(self) -> str:
        """
        Get a formatted period name for the search.
        """
        return f"{self.check_in} to {self.check_out}"

    def get_sub_directory(self) -> str:
        """
        Get the subdirectory path for storing search results.
        """
        return f"{self.area}/{self.guests}/{self.check_in}_{self.check_out}"


class BookingSearchPeriodRequest(BaseModel):
    """
    Model representing a booking.com period search request.
    """

    area: str = Field(..., description="The area to search for")
    guests: int = Field(2, description="Number of guests")
    date_from: datetime = Field(..., description="Start date of the period")
    date_upto: datetime = Field(..., description="End date of the period")
    max_concurrent: Optional[int] = Field(2, description="Maximum number of concurrent searches")

    def get_period_name(self) -> str:
        """
        Get a formatted period name for the search.
        """
        return f"{self.date_from.strftime('%Y-%m-%d')} to {self.date_upto.strftime('%Y-%m-%d')}"

    def get_sub_directory(self) -> str:
        """
        Get the subdirectory path for storing search results.
        """
        return f"{self.area}/{self.guests}/period_{self.date_from.strftime('%Y-%m-%d')}_{self.date_upto.strftime('%Y-%m-%d')}"



