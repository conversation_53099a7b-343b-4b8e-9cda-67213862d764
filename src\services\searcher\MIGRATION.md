# Migration to uv Package Manager

This document outlines the migration process from pip/venv to the uv package manager for the Searcher service.

## What is uv?

[uv](https://github.com/astral-sh/uv) is a modern Python package manager and resolver written in Rust. It's designed to be a drop-in replacement for pip and virtualenv with significant performance improvements:

- Up to 10-100x faster than pip for installations
- Reliable dependency resolution
- Reproducible builds with lockfiles
- Compatible with existing Python tooling

## Changes Made

The following changes were made to migrate the Searcher service to use uv:

1. **Added pyproject.toml**
   - Created a pyproject.toml file with all dependencies from requirements.txt
   - Added project metadata and build configuration
   - Includes modern dependencies like aiofiles for async I/O operations

2. **Created uv.lock file**
   - Generated a lock file for reproducible builds
   - Locked all dependencies to specific versions
   - Ensures consistent environments across deployments

3. **Updated README.md**
   - Added instructions for using uv for virtual environment creation
   - Updated dependency installation commands
   - Added examples for running commands with uv
   - Documented async storage capabilities and performance benefits

## Benefits of Using uv

- **Faster Installations**: Significantly faster dependency installation
- **Reproducible Builds**: Lock files ensure consistent environments
- **Better Caching**: Improved Docker build times
- **Modern Tooling**: Aligns with current Python packaging best practices

## How to Use uv

### Installation

Install uv using pip:

```bash
pip install uv
```

Or using the recommended installation method from the uv documentation:

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Creating Virtual Environments

Create a virtual environment:

```bash
uv venv
```

Activate the virtual environment:

```bash
# On Windows
.venv\Scripts\activate

# On Unix/MacOS
source .venv/bin/activate
```

### Installing Dependencies

Install dependencies from pyproject.toml:

```bash
# Install in development mode
uv pip install -e .

# Or install with specific extras
uv pip install -e ".[dev]"
```

For the most reproducible builds, use the lock file:

```bash
# Generate a lock file
uv lock

# Install from the lock file
uv sync
```

### Running Commands

Run commands in the virtual environment:

```bash
# Run a Python module
uv run python -m src.main serve

# Run a specific script
uv run src/scripts/example.py

# Run with additional dependencies
uv run --with rich python -m src.main serve
```

Run tools in isolated environments:

```bash
# Run pytest in an isolated environment
uvx pytest

# Run a specific version of a tool
uvx ruff@0.3.0 check
```

## Maintaining Dependencies

### Adding New Dependencies

Add new dependencies to pyproject.toml:

```toml
[project]
dependencies = [
    # Existing dependencies...
    "new-package>=1.0.0",
]
```

Or use the `uv add` command:

```bash
# Add a regular dependency
uv add new-package

# Add a development dependency
uv add --dev pytest

# Add with version constraint
uv add 'requests==2.31.0'
```

Then update the lock file:

```bash
# Generate or update the lock file
uv lock

# For a complete refresh, use --upgrade
uv lock --upgrade
```

### Updating Dependencies

To update all dependencies:

```bash
uv pip install -e . --upgrade
uv lock
```

To update a specific dependency:

```bash
uv pip install package-name --upgrade
uv lock
```

## Troubleshooting

If you encounter issues with uv:

1. Ensure you have the latest version: `pip install -U uv`
2. Try removing the virtual environment and recreating it: `rm -rf .venv && uv venv`
3. Check the [uv documentation](https://github.com/astral-sh/uv) for specific issues

## References

- [uv GitHub Repository](https://github.com/astral-sh/uv)
- [Python Packaging User Guide](https://packaging.python.org/)
- [PEP 621 – Storing project metadata in pyproject.toml](https://peps.python.org/pep-0621/)
