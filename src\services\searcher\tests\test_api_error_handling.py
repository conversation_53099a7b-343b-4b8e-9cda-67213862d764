"""
Test API Error Handling

This module tests that the API endpoints properly return error information
in SearchStatus responses when searches fail.
"""

import pytest
from unittest.mock import Mock
from fastapi.testclient import TestClient
from src.main import app
from src.services.search_service import SearchService


class TestAPIErrorHandling:
    """Test error handling in API endpoints."""

    @pytest.fixture
    def client(self):
        """Create a test client for the FastAPI app."""
        return TestClient(app)

    @pytest.fixture
    def mock_search_service_with_error(self, monkeypatch):
        """Mock the search service to simulate an error."""
        # Create a mock search service
        mock_service = Mock(spec=SearchService)
        
        # Mock the search method to simulate failure
        async def mock_search(request, output_dir):
            from src.services.search_service import SearchResult
            return SearchResult(
                area=request.area,
                guests=request.guests,
                period=request.get_period_name(),
                success=False,
                output_path=None,
                elapsed_time=5.0,
                error="Simulated search failure"
            )
        
        mock_service.search = mock_search
        
        # Mock the get_search_statuses method
        def mock_get_statuses():
            from src.models.search_status import SearchStatus
            return [SearchStatus(
                id="1",
                area="Test Area",
                guests="2",
                period="2025-07-01 to 2025-07-08",
                status="failed",
                started_at="2025-01-27T10:00:00",
                completed_at="2025-01-27T10:00:05",
                output_path="",
                error="Simulated search failure"
            )]
        
        mock_service.get_search_statuses = mock_get_statuses
        
        # Replace the search service in the controller
        from src.controllers import search_controller
        monkeypatch.setattr(search_controller, "search_service", mock_service)
        
        return mock_service

    def test_search_status_endpoint_returns_error_info(self, client, mock_search_service_with_error):
        """Test that the /search/status endpoint returns error information for failed searches."""
        # Get search status
        response = client.get("/api/v1/search/status")
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        
        # Verify structure
        assert "searches" in data
        assert len(data["searches"]) == 1
        
        # Verify error information is included
        search_status = data["searches"][0]
        assert search_status["status"] == "failed"
        assert search_status["error"] == "Simulated search failure"
        assert search_status["output_path"] == ""
        assert search_status["completed_at"] != ""

    def test_search_status_includes_elapsed_time(self, client, mock_search_service_with_error):
        """Test that the SearchStatus includes the elapsed_time calculated property."""
        # Get search status
        response = client.get("/api/v1/search/status")
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        
        # Verify elapsed_time is included
        search_status = data["searches"][0]
        assert "elapsed_time" in search_status
        assert search_status["elapsed_time"] == "5.0"  # Should be calculated from timestamps

    @pytest.fixture
    def mock_search_service_with_success(self, monkeypatch):
        """Mock the search service to simulate success."""
        # Create a mock search service
        mock_service = Mock(spec=SearchService)
        
        # Mock the get_search_statuses method for success case
        def mock_get_statuses():
            from src.models.search_status import SearchStatus
            return [SearchStatus(
                id="1",
                area="Test Area",
                guests="2",
                period="2025-07-01 to 2025-07-08",
                status="completed",
                started_at="2025-01-27T10:00:00",
                completed_at="2025-01-27T10:00:10",
                output_path="/test/output/path.html",
                error=""  # Empty string for successful searches
            )]
        
        mock_service.get_search_statuses = mock_get_statuses
        
        # Replace the search service in the controller
        from src.controllers import search_controller
        monkeypatch.setattr(search_controller, "search_service", mock_service)
        
        return mock_service

    def test_successful_search_status_no_error(self, client, mock_search_service_with_success):
        """Test that successful searches have empty error field."""
        # Get search status
        response = client.get("/api/v1/search/status")
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        
        # Verify structure
        assert "searches" in data
        assert len(data["searches"]) == 1
        
        # Verify no error information for successful search
        search_status = data["searches"][0]
        assert search_status["status"] == "completed"
        assert search_status["error"] == ""  # Should be empty string
        assert search_status["output_path"] == "/test/output/path.html"
        assert search_status["completed_at"] != ""
        assert search_status["elapsed_time"] == "10.0"  # Should be calculated

    def test_search_status_response_model_validation(self, client, mock_search_service_with_error):
        """Test that the API response validates against the SearchStatusResponse model."""
        # Get search status
        response = client.get("/api/v1/search/status")
        
        # Verify response structure matches the model
        assert response.status_code == 200
        data = response.json()
        
        # Verify top-level structure
        assert isinstance(data, dict)
        assert "searches" in data
        assert isinstance(data["searches"], list)
        
        # Verify SearchStatus structure
        if data["searches"]:
            search_status = data["searches"][0]
            required_fields = ["id", "area", "guests", "period", "status", "started_at"]
            optional_fields = ["completed_at", "output_path", "error", "elapsed_time"]
            
            for field in required_fields:
                assert field in search_status
                assert search_status[field] is not None
            
            for field in optional_fields:
                assert field in search_status
                # These can be empty strings or None, but should be present
