# Searcher Service Tests

This directory contains test files for the Searcher service.

## Purpose

- Ensures the Searcher service correctly scrapes data from Booking.com
- Validates browser automation functionality
- Tests handling of various search parameters and apartment URLs
- Verifies proper HTML storage and organization with async I/O operations
- Tests apartment download functionality and data extraction
- Validates asynchronous storage operations and performance

## Expected Content

This directory will contain:
- Unit tests for search parameter handling
- Integration tests for browser automation
- Mock responses for testing without actual web requests
- Test fixtures for different search scenarios
- Performance tests for scraping efficiency
- Tests for result objects (SearchResult, PeriodSearchResult, and ApartmentDownloadResult)
- Tests for search status models (SearchStatus, SearchStatusResponse from `src.models.search_status`)
- Tests for apartment download status models (ApartmentDownloadStatus, ApartmentDownloadStatusResponse from `src.models.apartment_download_status`)
- Tests for apartment download request models (ApartmentDownloadRequest from `src.models.apartment_download_request`)
- Validation tests for result object structure and data integrity
- Pydantic model validation tests for API endpoints with new model structure
- URL validation tests for apartment download requests
- Tests for computed `elapsed_time` properties in status models
- Tests for asynchronous storage operations and non-blocking I/O
- Performance tests for concurrent storage operations
- Tests for aiofiles integration and async file handling

## Test Guidelines

When implementing tests for this service:

### Core Functionality Tests
- Test various search parameters (locations, dates, guests)
- Test apartment URL validation and processing
- Verify correct handling of Booking.com's UI elements
- Test pagination and scrolling functionality
- Ensure proper storage of HTML content (both search results and apartment pages)
- Validate error handling for failed searches and downloads
- Test retry mechanisms for intermittent failures
- Test apartment data extraction and JSON generation
- Test async storage operations and file I/O performance
- Verify non-blocking behavior during concurrent storage operations
- Test aiofiles integration and error handling

### Result Object Tests
- Test SearchResult object creation and field population
- Test ApartmentDownloadResult object creation and field population
- Verify PeriodSearchResult aggregation and error handling
- Validate result object serialization (to_dict() methods)
- Test timing accuracy in elapsed_time fields
- Verify field order (contextual fields first: area, guests, period for searches; url, apartment_id for downloads)
- Test result object consistency across different scenarios
- Test apartment ID extraction from various URL formats

### Timeout Configuration Tests
- Test page_timeout configuration across all browser classes
- Test click_timeout configuration across all browser classes
- Verify timeout values are correctly read from environment variables
- Test timeout behavior under different network conditions
- Validate timeout consistency between BrowserService, BookingBrowser, and BrowserScrapperPlaywright

### API Model Tests
- Test SearchStatus model validation and field types (from `src.models.search_status`)
- Test SearchStatusResponse model structure (from `src.models.search_status`)
- Test ApartmentDownloadStatus model validation and field types (from `src.models.apartment_download_status`)
- Test ApartmentDownloadStatusResponse model structure (from `src.models.apartment_download_status`)
- Verify Pydantic model serialization and deserialization across all model files
- Test API endpoint response validation with new model structure
- Validate status value constraints (in_progress, completed, failed) for both search and apartment download models
- Test timestamp format validation (ISO format) across all status models
- Test optional field handling (completed_at, output_path, error) in all status models
- Test computed `elapsed_time` property calculation in both SearchStatus and ApartmentDownloadStatus models
- Verify model import paths and dependencies

### Async Storage Tests
- Test BrowserScrapperStoreAbstract async method signatures
- Test BrowserScrapperStorePath with aiofiles operations
- Test BrowserScrapperStoreAzure async Azure operations
- Verify async/await patterns in all storage implementations
- Test concurrent storage operations and performance
- Validate error handling in async storage contexts
- Test storage operation timing and non-blocking behavior

### Service Integration Tests
- Test SearchService with different result scenarios
- Test SearchService status tracking functionality
- Test PeriodSearchService with various batch sizes
- Verify progress callback functionality
- Test concurrency control and error isolation
- Test search status updates during operation lifecycle
- Test async storage integration across all services

## Running Tests

Tests should be executable via standard testing frameworks and integrated with the CI/CD pipeline to ensure service reliability.

## Notes

- Some tests may require mocking external services to avoid actual web requests
- Consider using recorded HTTP responses for consistent testing
- Include tests for the auto-scroll functionality to ensure all results are captured
