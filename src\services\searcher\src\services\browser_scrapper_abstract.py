"""
Browser Scrapper Abstract

This module provides an abstract class for browser scrappers.
"""

from abc import ABC, abstractmethod

from src.stores.browser_scrapper_store_abstract import BrowserScrapperStoreAbstract


class BrowserScrapperAbstract(ABC):
    """
    Abstract class for browser scrappers.
    """

    def __init__(self, url: str, store: BrowserScrapperStoreAbstract) -> None:
        """
        Initialize the BrowserScrapper with a URL and browser scrapper store.

        Args:
            url: The URL to scrape
            store: The store to save the results to
        """
        self.url: str = url
        self.store: BrowserScrapperStoreAbstract = store

    async def _save_content(self, content: str, sub_dir: str = None) -> str:
        """
        Save HTML content to file with timestamp.

        Args:
            content: The HTML content to save
            sub_dir: The subdirectory to save the content to

        Returns:
            str: The path to the saved file
        """
        return await self.store.save_content(self.url, content, ".html", sub_dir)

    async def download_page(self, sub_dir: str = None) -> str:
        """
        Download page content and save with timestamp.

        Args:
            sub_dir: The subdirectory to save the content to

        Returns:
            str: The path to the saved file
        """
        content = await self.download_page_content()
        return await self._save_content(content, sub_dir)

    @abstractmethod
    async def download_page_content(self) -> str:
        """
        Download page content string.

        Returns:
            str: The HTML content of the page
        """
