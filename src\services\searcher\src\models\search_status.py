"""
Search Status Models

This module defines the status models for search operations.
"""

from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, computed_field


class SearchStatus(BaseModel):
    """
    Model representing the status of a search operation.
    """

    id: str = Field(..., description="Unique identifier for the search")
    area: str = Field(..., description="The area being searched")
    guests: str = Field(..., description="Number of guests")
    period: str = Field(..., description="Search period")
    status: str = Field(..., description="Current status of the search")
    started_at: str = Field(..., description="When the search was started")
    completed_at: Optional[str] = Field(None, description="When the search was completed")
    output_path: Optional[str] = Field(None, description="Path to the search results")
    error: Optional[str] = Field(None, description="Error message if search failed")

    @computed_field
    @property
    def elapsed_time(self) -> str:
        """
        Calculate elapsed time in seconds between started_at and completed_at.
        Returns the elapsed time as a string, or empty string if not completed.
        """
        if not self.completed_at:
            return ""

        try:
            started = datetime.fromisoformat(self.started_at)
            completed = datetime.fromisoformat(self.completed_at)
            seconds = (completed - started).total_seconds()
            return str(seconds)
        except (ValueError, TypeError):
            return ""


class SearchStatusResponse(BaseModel):
    """
    Model representing the response for search status endpoint.
    """

    searches: List[SearchStatus] = Field(..., description="List of search statuses")
