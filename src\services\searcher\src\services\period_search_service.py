"""
Period Search Service

This module provides common functionality for running period searches
across multiple weeks with concurrency control.
"""

import asyncio
import logging
import time
from typing import List, Tuple, Dict, Any, Optional, Callable
from datetime import datetime, timedelta

from src.models.booking_search_request import BookingSearchRequest
from src.services.search_service import SearchService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class PeriodSearchResult:
    """Result of a period search operation."""

    def __init__(self, total_weeks: int, successful: int, failed: int, errors: List[str], elapsed_time: float):
        self.total_weeks = total_weeks
        self.successful = successful
        self.failed = failed
        self.errors = errors
        self.elapsed_time = elapsed_time

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "total_weeks": self.total_weeks,
            "successful": self.successful,
            "failed": self.failed,
            "errors": self.errors,
            "elapsed_time": self.elapsed_time
        }


class PeriodSearchService:
    """Service for handling period searches across multiple weeks."""

    def __init__(self, search_service: Optional[SearchService] = None):
        """
        Initialize the period search service.

        Args:
            search_service: Optional SearchService instance. If not provided, creates a new one.
        """
        self.search_service = search_service or SearchService()

    async def run_period_search(
        self,
        area: str,
        guests: int,
        week_parts: List[Tuple[datetime, datetime]],
        output_dir: str,
        max_concurrent: int,
        progress_callback: Optional[Callable[[int, int, str, bool], None]] = None
    ) -> PeriodSearchResult:
        """
        Run searches for multiple week parts asynchronously with concurrency control.

        Args:
            area: The area to search
            guests: Number of guests
            week_parts: List of (start_date, end_date) tuples for week parts
            output_dir: Directory to save results
            max_concurrent: Maximum number of concurrent searches
            progress_callback: Optional callback function for progress reporting
                              Signature: (week_part_num, total_week_parts, period_name, success)

        Returns:
            PeriodSearchResult: Result summary of the period search
        """
        if not week_parts:
            return PeriodSearchResult(0, 0, 0, ["No week parts provided"], 0.0)

        # Record start time
        start_time = time.time()

        # Get current date and tomorrow's date for validation
        current_date = datetime.now().date()
        tomorrow_date = current_date + timedelta(days=1)

        # Create a semaphore to limit concurrent searches
        semaphore = asyncio.Semaphore(max_concurrent)
        tasks = []
        valid_week_part_count = 0

        for i, (start_date, end_date) in enumerate(week_parts, 1):
            # Convert datetime to date for comparison
            start_date_only = start_date.date()
            end_date_only = end_date.date()

            # Skip if end_date is before current date + 1 day (tomorrow)
            if end_date_only < tomorrow_date:
                logger.info(f"Skipping week part {i}: end date {end_date_only} is before tomorrow {tomorrow_date}")
                continue

            # Adjust start_date if it's <= current_date
            adjusted_start_date = start_date
            if start_date_only <= current_date:
                # Set start_date to current date but keep the time component
                adjusted_start_date = datetime.combine(current_date, start_date.time())
                logger.info(f"Adjusting week part {i}: start date changed from {start_date_only} to {current_date}")

            valid_week_part_count += 1

            # Create a BookingSearchRequest for each valid week part
            # Convert datetime objects to strings
            check_in_str = adjusted_start_date.strftime("%Y-%m-%d")
            check_out_str = end_date.strftime("%Y-%m-%d")

            request = BookingSearchRequest(
                area=area,
                guests=guests,
                check_in=check_in_str,
                check_out=check_out_str
            )

            # Create a task for each search with semaphore control
            task = self._search_single_period_with_semaphore(
                semaphore, request, output_dir, valid_week_part_count, len(week_parts), progress_callback
            )
            tasks.append(task)

        # Run all searches concurrently (but limited by semaphore)
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Analyze results
            successful = sum(1 for result in results if not isinstance(result, Exception))
            failed = len(results) - successful
            errors = [str(result) for result in results if isinstance(result, Exception)]
        else:
            # No valid tasks to run
            results = []
            successful = 0
            failed = 0
            errors = []

        # Calculate elapsed time
        elapsed_time = time.time() - start_time

        # Calculate skipped count
        skipped = len(week_parts) - valid_week_part_count

        # Create result object with elapsed time (using original week_parts count for total)
        result = PeriodSearchResult(len(week_parts), successful, failed, errors, elapsed_time)

        # Log the period search result
        if skipped > 0:
            logger.info(f"Period search completed for {area} with {guests} guests: {len(week_parts)} total week parts, {skipped} skipped, {valid_week_part_count} processed ({successful} successful, {failed} failed) in {elapsed_time:.2f} seconds")
        else:
            logger.info(f"Period search completed for {area} with {guests} guests across {len(week_parts)} week parts: {successful} successful, {failed} failed in {elapsed_time:.2f} seconds")

        return result

    async def _search_single_period_with_semaphore(
        self,
        semaphore: asyncio.Semaphore,
        request: BookingSearchRequest,
        output_dir: str,
        week_part_num: int,
        total_week_parts: int,
        progress_callback: Optional[Callable[[int, int, str, bool], None]] = None
    ) -> bool:
        """
        Search for a single period with semaphore-controlled concurrency.

        Args:
            semaphore: Asyncio semaphore to control concurrency
            request: The BookingSearchRequest
            output_dir: Directory to save results
            week_part_num: Current week part number
            total_week_parts: Total number of week parts
            progress_callback: Optional callback for progress reporting

        Returns:
            bool: True if successful, raises exception if failed
        """
        async with semaphore:
            try:
                # Report start if callback provided
                if progress_callback:
                    progress_callback(week_part_num, total_week_parts, request.get_period_name(), None)

                search_result = await self.search_service.search(request, output_dir)

                # Check if search was successful
                if search_result.success:
                    # Report success if callback provided
                    if progress_callback:
                        progress_callback(week_part_num, total_week_parts, request.get_period_name(), True)
                    return True
                else:
                    # Report failure if callback provided
                    if progress_callback:
                        progress_callback(week_part_num, total_week_parts, request.get_period_name(), False)
                    raise Exception(f"Search failed: {search_result.error}")
            except Exception as e:
                # Report failure if callback provided
                if progress_callback:
                    progress_callback(week_part_num, total_week_parts, request.get_period_name(), False)

                raise Exception(f"Week part {week_part_num}/{total_week_parts} failed for {request.get_period_name()}: {str(e)}")
