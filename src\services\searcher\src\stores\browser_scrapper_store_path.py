"""
Browser Scrapper Store Path

This module provides a file system implementation of the browser scrapper store.
"""

import os
import datetime
import aiofiles

from src.stores.browser_scrapper_store_abstract import BrowserScrapperStoreAbstract


class BrowserScrapperStorePath(BrowserScrapperStoreAbstract):
    """
    File system implementation of the browser scrapper store.
    """
    
    async def save_content(self, url: str, content: str, file_extension: str, sub_dir: str = None) -> str:
        """
        Save string content to file.

        Args:
            url: The URL of the content
            content: The content to save
            file_extension: The file extension to use
            sub_dir: The subdirectory to save the content to

        Returns:
            str: The path to the saved file
        """
        # Create output directory if it doesn't exist
        output_dir = self.output_dir
        if sub_dir:
            output_dir = output_dir / sub_dir

        os.makedirs(output_dir, exist_ok=True)

        # Create file name with timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"{timestamp}{file_extension}"
        file_path = output_dir / file_name

        # Write content to file asynchronously
        async with aiofiles.open(file_path, "w", encoding="utf-8") as f:
            await f.write(content)

        return str(file_path)
