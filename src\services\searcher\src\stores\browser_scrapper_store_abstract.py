"""
Browser Scrapper Store Abstract

This module provides an abstract class for browser scrapper stores.
"""

from abc import ABC, abstractmethod
from pathlib import Path


class BrowserScrapperStoreAbstract(ABC):
    """
    Abstract class for browser scrapper stores.
    """
    
    def __init__(self, output_dir: str) -> None:
        """
        Initialize the BrowserScrapperStore with an output directory.
        
        Args:
            output_dir: The directory to save the results to
        """
        self.output_dir: Path = Path(output_dir)

    @abstractmethod
    async def save_content(self, url: str, content: str, file_extension: str, sub_dir: str = None) -> str:
        """
        Save string content to file.

        Args:
            url: The URL of the content
            content: The content to save
            file_extension: The file extension to use
            sub_dir: The subdirectory to save the content to

        Returns:
            str: The path to the saved file
        """
