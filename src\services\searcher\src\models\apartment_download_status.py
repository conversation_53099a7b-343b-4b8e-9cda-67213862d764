"""
Apartment Download Status Models

This module defines the status models for apartment download operations.
"""

from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, computed_field


class ApartmentDownloadStatus(BaseModel):
    """
    Model representing the status of an apartment download operation.
    """

    apartment_id: str = Field(..., description="Extracted apartment/property ID")
    url: str = Field(..., description="The apartment URL being downloaded")
    status: str = Field(..., description="Current status of the download")
    started_at: str = Field(..., description="When the download was started")
    completed_at: Optional[str] = Field(None, description="When the download was completed")
    output_path: Optional[str] = Field(None, description="Path to the download results")
    error: Optional[str] = Field(None, description="Error message if download failed")

    @computed_field
    @property
    def elapsed_time(self) -> str:
        """
        Calculate elapsed time in seconds between started_at and completed_at.
        Returns the elapsed time as a string, or empty string if not completed.
        """
        if not self.completed_at:
            return ""

        try:
            started = datetime.fromisoformat(self.started_at)
            completed = datetime.fromisoformat(self.completed_at)
            seconds = (completed - started).total_seconds()
            return str(seconds)
        except (ValueError, TypeError):
            return ""


class ApartmentDownloadStatusResponse(BaseModel):
    """
    Model representing the response for apartment download status endpoint.
    """

    downloads: List[ApartmentDownloadStatus] = Field(..., description="List of apartment download statuses")
