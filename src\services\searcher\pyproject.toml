[project]
name = "searcher"
version = "0.1.0"
description = "Service for searching and scraping rental listings from booking platforms"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aiofiles>=24.1.0",
    "azure-servicebus>=7.11.0",
    "azure-storage-blob>=12.17.0",
    "azure-storage-file-share>=12.15.0",
    "bs4>=0.0.2",
    "click>=8.1.7",
    "fastapi>=0.95.0",
    "pandas>=2.2.3",
    "playwright>=1.42.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.1.0",
    "uvicorn>=0.22.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "autoflake>=2.2.0",
    "black>=23.3.0",
    "isort>=5.12.0"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
asyncio_mode = "auto"

[tool.black]
line-length = 88
target-version = ["py311"]

[tool.isort]
profile = "black"
line_length = 88

[dependency-groups]
dev = [
    "httpx>=0.28.1",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.26.0",
]
